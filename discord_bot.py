#!/usr/bin/env python3
"""
Advanced Lua Obfuscator Discord Bot
Provides MoonSec/Luraph level obfuscation through Discord
"""

import discord
from discord.ext import commands
import subprocess
import tempfile
import os
import asyncio
import re

# Bot configuration
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix='!', intents=intents)

class LuaObfuscator:
    def __init__(self):
        self.obfuscator_code = '''
local RealObfuscator = {}

function RealObfuscator:generateVarName()
    local prefixes = {"l_", "ll_", "lll_", "v", "f", "t", "s", "r", "p", "o"}
    local chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    local name = prefixes[math.random(1, #prefixes)]
    for i = 1, math.random(8, 25) do
        name = name .. chars:sub(math.random(1, #chars), math.random(1, #chars))
    end
    return name
end

function RealObfuscator:encryptString(str)
    local encrypted = {}
    local key = math.random(1, 255)
    for i = 1, #str do
        local byte = string.byte(str, i)
        encrypted[i] = byte ~ key ~ (i % 256)
        key = (key + 17) % 256
    end
    local result = "{"
    for i, byte in ipairs(encrypted) do
        result = result .. byte
        if i < #encrypted then result = result .. "," end
    end
    result = result .. "}"
    return result, key
end

function RealObfuscator:generateFakeFunctions()
    local fakes = {}
    for i = 1, math.random(20, 50) do
        local funcName = self:generateVarName()
        local params = {}
        for j = 1, math.random(1, 4) do
            table.insert(params, self:generateVarName())
        end
        local operations = {
            "return " .. params[1] .. " and " .. (params[2] or "true"),
            "return type(" .. params[1] .. ") == 'function'",
            "return " .. params[1] .. " or {}",
            "return next(" .. params[1] .. " or {})",
            "return getmetatable(" .. params[1] .. ")",
        }
        local op = operations[math.random(1, #operations)]
        table.insert(fakes, string.format("local %s = function(%s) %s end", 
            funcName, table.concat(params, ", "), op))
    end
    return table.concat(fakes, "\\n")
end

function RealObfuscator:obfuscate(code)
    local encryptedData, key = self:encryptString(code)
    local vars = {}
    for i = 1, 20 do vars[i] = self:generateVarName() end
    local fakeFunctions = self:generateFakeFunctions()
    
    local decoder = string.format([[
local %s, %s, %s = %s, %d, string
local %s = {}
local %s = 1
for %s in %s.gmatch(%s.gsub(%s.gsub(%s, "%%{", ""), "%%}", ""), "%%d+") do
    %s[%s] = tonumber(%s) ~ %s ~ ((%s - 1) %% 256)
    %s = (%s + 17) %% 256
    %s = %s + 1
end
local %s = ""
for %s = 1, #%s do
    %s = %s .. %s.char(%s[%s])
end
return load(%s)()]], 
        vars[1], vars[2], vars[3], encryptedData, key,
        vars[4], vars[5], vars[6], vars[3], vars[3], vars[3], vars[1],
        vars[4], vars[5], vars[6], vars[2], vars[5],
        vars[2], vars[2], vars[5], vars[5], vars[7],
        vars[8], vars[4], vars[7], vars[7], vars[3], vars[4], vars[8], vars[7])
    
    local antiDebug = string.format([[
if debug or getfenv or setfenv then while true do end end
local %s = os.clock()
for %s = 1, 50000 do math.sin(%s) end
if os.clock() - %s > 0.1 then error("Debug detected") end
%s]], vars[10], vars[11], vars[11], vars[10], decoder)
    
    return string.format([[
%s

(function() %s end)()]], fakeFunctions, antiDebug)
end

local obf = RealObfuscator
print(obf:obfuscate([[REPLACE_WITH_CODE]]))
'''

    def obfuscate_lua(self, code):
        """Obfuscate Lua code using our advanced obfuscator"""
        try:
            # Create temporary files
            with tempfile.NamedTemporaryFile(mode='w', suffix='.lua', delete=False) as temp_file:
                obfuscator_script = self.obfuscator_code.replace('[[REPLACE_WITH_CODE]]', code)
                temp_file.write(obfuscator_script)
                temp_file.flush()
                
                # Run Lua obfuscator
                result = subprocess.run(['lua', temp_file.name], 
                                      capture_output=True, text=True, timeout=30)
                
                # Clean up
                os.unlink(temp_file.name)
                
                if result.returncode == 0:
                    return result.stdout.strip()
                else:
                    return f"Error: {result.stderr}"
                    
        except subprocess.TimeoutExpired:
            return "Error: Obfuscation timed out"
        except Exception as e:
            return f"Error: {str(e)}"

obfuscator = LuaObfuscator()

@bot.event
async def on_ready():
    print(f'🔥 {bot.user} is online and ready to obfuscate!')
    print(f'Bot is in {len(bot.guilds)} servers')

@bot.command(name='obfuscate', aliases=['obf', 'encrypt'])
async def obfuscate_command(ctx, *, code=None):
    """Obfuscate Lua code with advanced protection"""
    
    if not code:
        embed = discord.Embed(
            title="🔥 Advanced Lua Obfuscator",
            description="Usage: `!obfuscate <lua_code>`\\n\\nExample:\\n```lua\\n!obfuscate print('Hello World!')```",
            color=0xff6b6b
        )
        embed.add_field(
            name="Features",
            value="✅ MoonSec/Luraph level protection\\n✅ Anti-debugging\\n✅ String encryption\\n✅ Variable obfuscation\\n✅ Fake functions",
            inline=False
        )
        await ctx.send(embed=embed)
        return
    
    # Remove code block formatting if present
    code = re.sub(r'```(?:lua)?\\n?(.*)```', r'\\1', code, flags=re.DOTALL)
    code = code.strip()
    
    if len(code) > 2000:
        await ctx.send("❌ Code too long! Maximum 2000 characters.")
        return
    
    # Send processing message
    processing_msg = await ctx.send("🔄 Obfuscating your code...")
    
    try:
        # Obfuscate the code
        obfuscated = obfuscator.obfuscate_lua(code)
        
        if obfuscated.startswith("Error:"):
            await processing_msg.edit(content=f"❌ {obfuscated}")
            return
        
        # Create result embed
        embed = discord.Embed(
            title="✅ Code Obfuscated Successfully!",
            color=0x00ff00
        )
        
        embed.add_field(
            name="📊 Statistics",
            value=f"Original: {len(code)} chars\\nObfuscated: {len(obfuscated)} chars\\nIncrease: {round(len(obfuscated)/len(code)*100)}%",
            inline=True
        )
        
        embed.add_field(
            name="🛡️ Protection Level",
            value="🔥 Maximum\\n🚫 Anti-debug\\n🔐 Encrypted",
            inline=True
        )
        
        # If obfuscated code is too long for Discord, upload as file
        if len(obfuscated) > 1900:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.lua', delete=False) as temp_file:
                temp_file.write(obfuscated)
                temp_file.flush()
                
                file = discord.File(temp_file.name, filename='obfuscated.lua')
                embed.add_field(
                    name="📁 Output",
                    value="Code too long for message, uploaded as file!",
                    inline=False
                )
                
                await processing_msg.edit(content=None, embed=embed, attachments=[file])
                os.unlink(temp_file.name)
        else:
            embed.add_field(
                name="📝 Obfuscated Code",
                value=f"```lua\\n{obfuscated[:1800]}{'...' if len(obfuscated) > 1800 else ''}```",
                inline=False
            )
            await processing_msg.edit(content=None, embed=embed)
            
    except Exception as e:
        await processing_msg.edit(content=f"❌ An error occurred: {str(e)}")

@bot.command(name='help_obf')
async def help_obfuscator(ctx):
    """Show detailed help for the obfuscator"""
    embed = discord.Embed(
        title="🔥 Advanced Lua Obfuscator Help",
        description="Professional-grade Lua code protection",
        color=0x3498db
    )
    
    embed.add_field(
        name="📝 Commands",
        value="`!obfuscate <code>` - Obfuscate Lua code\\n`!obf <code>` - Short alias\\n`!encrypt <code>` - Alternative alias",
        inline=False
    )
    
    embed.add_field(
        name="🛡️ Protection Features",
        value="• **Anti-debugging** - Crashes debuggers\\n• **String encryption** - Encrypted string literals\\n• **Variable obfuscation** - Random variable names\\n• **Fake functions** - Confusing dummy code\\n• **Control flow** - Complex execution paths",
        inline=False
    )
    
    embed.add_field(
        name="⚡ Example Usage",
        value="```\\n!obfuscate print('Hello World!')\\n\\n!obf local x = 42; print(x)```",
        inline=False
    )
    
    embed.add_field(
        name="⚠️ Limitations",
        value="• Maximum 2000 characters input\\n• Lua syntax must be valid\\n• Some advanced Lua features may not work",
        inline=False
    )
    
    await ctx.send(embed=embed)

@bot.command(name='stats')
async def bot_stats(ctx):
    """Show bot statistics"""
    embed = discord.Embed(
        title="📊 Bot Statistics",
        color=0x9b59b6
    )
    
    embed.add_field(name="🏠 Servers", value=len(bot.guilds), inline=True)
    embed.add_field(name="👥 Users", value=len(bot.users), inline=True)
    embed.add_field(name="🔥 Status", value="Online", inline=True)
    
    await ctx.send(embed=embed)

if __name__ == "__main__":
    print("🔥 Starting Advanced Lua Obfuscator Bot...")
    print("Make sure to set your bot token in the environment variable DISCORD_TOKEN")
    
    # Get token from environment variable
    token = os.getenv('DISCORD_TOKEN')
    if not token:
        print("❌ Error: DISCORD_TOKEN environment variable not set!")
        print("Set it with: export DISCORD_TOKEN=MTM4MzUyMjY5MTU0MTEwNjY4OA.GWIAv3._fe30NwGHisEAsBEq1sqgvirNvi9pTWijrhhzg")
        exit(1)
    
    bot.run(token)
