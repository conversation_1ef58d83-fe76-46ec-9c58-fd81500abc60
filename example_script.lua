--[[
    Example <PERSON><PERSON> for Obfuscation Testing
    This script demonstrates various Lua features that the obfuscator should handle
]]

-- Global variables
local secretKey = "MySecretPassword123"
local importantData = {
    username = "admin",
    password = "super_secret",
    apiKey = "abc123def456",
    config = {
        debug = false,
        timeout = 30,
        retries = 3
    }
}

-- Mathematical functions
local function calculateHash(input)
    local hash = 0
    for i = 1, #input do
        hash = hash + string.byte(input, i) * i
    end
    return hash % 1000000
end

local function fi<PERSON><PERSON><PERSON>(n)
    if n <= 1 then
        return n
    end
    local a, b = 0, 1
    for i = 2, n do
        a, b = b, a + b
    end
    return b
end

-- String manipulation
local function encodeMessage(message, key)
    local encoded = ""
    for i = 1, #message do
        local char = string.byte(message, i)
        local keyChar = string.byte(key, ((i - 1) % #key) + 1)
        encoded = encoded .. string.char((char + keyChar) % 256)
    end
    return encoded
end

local function decodeMessage(encoded, key)
    local decoded = ""
    for i = 1, #encoded do
        local char = string.byte(encoded, i)
        local keyChar = string.byte(key, ((i - 1) % #key) + 1)
        decoded = decoded .. string.char((char - keyChar + 256) % 256)
    end
    return decoded
end

-- Table operations
local function deepCopy(original)
    local copy = {}
    for key, value in pairs(original) do
        if type(value) == "table" then
            copy[key] = deepCopy(value)
        else
            copy[key] = value
        end
    end
    return copy
end

local function mergeTable(t1, t2)
    local result = deepCopy(t1)
    for key, value in pairs(t2) do
        result[key] = value
    end
    return result
end

-- Authentication system
local function authenticate(username, password)
    local hashedPassword = calculateHash(password .. secretKey)
    local expectedHash = calculateHash(importantData.password .. secretKey)
    
    if username == importantData.username and hashedPassword == expectedHash then
        return true, "Authentication successful"
    else
        return false, "Invalid credentials"
    end
end

-- Data processing
local function processData(data)
    local processed = {}
    
    for key, value in pairs(data) do
        if type(value) == "string" then
            processed[key] = encodeMessage(value, secretKey)
        elseif type(value) == "number" then
            processed[key] = value * fibonacci(5)
        elseif type(value) == "table" then
            processed[key] = processData(value)
        else
            processed[key] = value
        end
    end
    
    return processed
end

-- Network simulation
local function simulateNetworkRequest(url, data)
    -- Simulate network delay
    local delay = math.random(100, 500)
    for i = 1, delay * 1000 do
        math.sin(i)
    end
    
    -- Simulate response
    local response = {
        status = 200,
        data = {
            success = true,
            message = "Request processed successfully",
            timestamp = os.time(),
            hash = calculateHash(url .. tostring(data))
        }
    }
    
    return response
end

-- Main application logic
local function runApplication()
    print("Starting secure application...")
    
    -- Authenticate user
    local success, message = authenticate("admin", "super_secret")
    if not success then
        error("Authentication failed: " .. message)
    end
    
    print("Authentication successful!")
    
    -- Process sensitive data
    local processedData = processData(importantData)
    print("Data processed successfully")
    
    -- Simulate API calls
    local apiResponse = simulateNetworkRequest("https://api.example.com/data", processedData)
    print("API Response:", apiResponse.data.message)
    
    -- Generate report
    local report = {
        timestamp = os.time(),
        user = importantData.username,
        dataHash = calculateHash(tostring(processedData)),
        fibonacciCheck = fibonacci(10),
        status = "completed"
    }
    
    return report
end

-- Error handling wrapper
local function safeExecute()
    local success, result = pcall(runApplication)
    
    if success then
        print("Application completed successfully!")
        print("Report:", tostring(result))
        return result
    else
        print("Application error:", result)
        return nil
    end
end

-- Conditional execution based on environment
if _G.game and _G.workspace then
    -- Roblox environment
    print("Running in Roblox environment")
    
    local Players = game:GetService("Players")
    local player = Players.LocalPlayer
    
    if player then
        print("Player detected:", player.Name)
        importantData.username = player.Name
    end
    
    -- Execute in spawn to avoid yielding issues
    spawn(function()
        local result = safeExecute()
        if result then
            -- Could store result in ReplicatedStorage or send to server
            print("Roblox execution completed")
        end
    end)
    
else
    -- Standard Lua environment
    print("Running in standard Lua environment")
    local result = safeExecute()
    
    if result then
        print("Final result:", string.format("%q", result))
    end
end

-- Export functions for testing (if needed)
return {
    calculateHash = calculateHash,
    fibonacci = fibonacci,
    encodeMessage = encodeMessage,
    decodeMessage = decodeMessage,
    authenticate = authenticate,
    processData = processData
}
