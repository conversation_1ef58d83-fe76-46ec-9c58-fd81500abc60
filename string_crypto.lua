--[[
    Advanced String Encryption System
    Multi-layer encryption with decoys and anti-tampering
]]

local StringCrypto = {}
StringCrypto.__index = StringCrypto

-- Advanced encryption algorithms
local function xorEncrypt(data, key)
    local result = {}
    for i = 1, #data do
        local keyByte = key[(i - 1) % #key + 1]
        result[i] = data[i] ~ keyByte
    end
    return result
end

local function substitutionEncrypt(data, sbox)
    local result = {}
    for i = 1, #data do
        result[i] = sbox[data[i] + 1]
    end
    return result
end

local function transpositionEncrypt(data, blockSize)
    local result = {}
    for i = 1, #data, blockSize do
        local block = {}
        for j = 0, blockSize - 1 do
            if data[i + j] then
                table.insert(block, data[i + j])
            end
        end
        -- Reverse and shuffle block
        for j = #block, 1, -1 do
            table.insert(result, block[j])
        end
    end
    return result
end

-- Generate cryptographically strong S-box
local function generateSBox(seed)
    math.randomseed(seed)
    local sbox = {}
    local used = {}
    
    -- Initialize with identity
    for i = 0, 255 do
        sbox[i + 1] = i
    end
    
    -- Fisher-<PERSON> shuffle
    for i = 256, 2, -1 do
        local j = math.random(1, i)
        sbox[i], sbox[j] = sbox[j], sbox[i]
    end
    
    return sbox
end

-- Generate inverse S-box for decryption
local function generateInverseSBox(sbox)
    local inverse = {}
    for i = 1, #sbox do
        inverse[sbox[i] + 1] = i - 1
    end
    return inverse
end

-- Advanced key derivation function
local function deriveKey(password, salt, iterations)
    local key = {}
    local hash = password .. salt
    
    for iter = 1, iterations do
        local newHash = ""
        for i = 1, #hash do
            local char = string.byte(hash, i)
            char = char ~ (iter % 256)
            char = (char * 69069 + 1) % 256
            newHash = newHash .. string.char(char)
        end
        hash = newHash
    end
    
    for i = 1, #hash do
        key[i] = string.byte(hash, i)
    end
    
    return key
end

-- Create fake decoy strings
local function generateDecoyStrings(count)
    local decoys = {}
    local fakeWords = {
        "function", "local", "return", "end", "if", "then", "else", "while", "do",
        "for", "in", "repeat", "until", "break", "true", "false", "nil", "and", "or", "not",
        "print", "type", "tostring", "tonumber", "pairs", "ipairs", "next", "getmetatable",
        "setmetatable", "rawget", "rawset", "rawlen", "string", "table", "math", "os"
    }
    
    for i = 1, count do
        local decoy = ""
        local wordCount = math.random(2, 8)
        for j = 1, wordCount do
            if j > 1 then decoy = decoy .. " " end
            decoy = decoy .. fakeWords[math.random(1, #fakeWords)]
        end
        table.insert(decoys, decoy)
    end
    
    return decoys
end

-- Main encryption function
function StringCrypto:encrypt(plaintext, masterKey)
    masterKey = masterKey or "DefaultMasterKey2024"
    
    -- Convert string to byte array
    local data = {}
    for i = 1, #plaintext do
        data[i] = string.byte(plaintext, i)
    end
    
    -- Generate encryption parameters
    local salt = {}
    for i = 1, 16 do
        salt[i] = math.random(0, 255)
    end

    local unpack = unpack or table.unpack
    local key = deriveKey(masterKey, string.char(unpack(salt)), 1000)
    local sbox = generateSBox(key[1] * 256 + key[2])
    local blockSize = math.max(4, math.min(16, #data // 4))
    
    -- Layer 1: XOR encryption
    data = xorEncrypt(data, key)
    
    -- Layer 2: Substitution
    data = substitutionEncrypt(data, sbox)
    
    -- Layer 3: Transposition
    data = transpositionEncrypt(data, blockSize)
    
    -- Layer 4: Final XOR with rotated key
    local rotatedKey = {}
    for i = 1, #key do
        rotatedKey[i] = key[(i + 7) % #key + 1]
    end
    data = xorEncrypt(data, rotatedKey)
    
    -- Generate decoy data
    local decoys = generateDecoyStrings(math.random(10, 30))
    
    return {
        encrypted = data,
        salt = salt,
        sboxSeed = key[1] * 256 + key[2],
        blockSize = blockSize,
        decoys = decoys,
        checksum = self:calculateChecksum(data)
    }
end

-- Decryption function
function StringCrypto:decrypt(encryptedData, masterKey)
    masterKey = masterKey or "DefaultMasterKey2024"
    
    -- Verify checksum
    if self:calculateChecksum(encryptedData.encrypted) ~= encryptedData.checksum then
        error("Tampering detected! Checksum mismatch.")
    end
    
    local data = {}
    for i, byte in ipairs(encryptedData.encrypted) do
        data[i] = byte
    end
    
    -- Regenerate encryption parameters
    local unpack = unpack or table.unpack
    local key = deriveKey(masterKey, string.char(unpack(encryptedData.salt)), 1000)
    local sbox = generateSBox(encryptedData.sboxSeed)
    local inverseSbox = generateInverseSBox(sbox)
    
    -- Reverse Layer 4: Final XOR
    local rotatedKey = {}
    for i = 1, #key do
        rotatedKey[i] = key[(i + 7) % #key + 1]
    end
    data = xorEncrypt(data, rotatedKey)
    
    -- Reverse Layer 3: Transposition
    data = self:reverseTransposition(data, encryptedData.blockSize)
    
    -- Reverse Layer 2: Substitution
    local result = {}
    for i = 1, #data do
        result[i] = inverseSbox[data[i] + 1]
    end
    data = result
    
    -- Reverse Layer 1: XOR
    data = xorEncrypt(data, key)
    
    -- Convert back to string
    local plaintext = ""
    for i = 1, #data do
        plaintext = plaintext .. string.char(data[i])
    end
    
    return plaintext
end

-- Reverse transposition cipher
function StringCrypto:reverseTransposition(data, blockSize)
    local result = {}
    local blocks = {}
    
    -- Split into blocks
    for i = 1, #data, blockSize do
        local block = {}
        for j = 0, blockSize - 1 do
            if data[i + j] then
                table.insert(block, data[i + j])
            end
        end
        table.insert(blocks, block)
    end
    
    -- Reverse each block
    for _, block in ipairs(blocks) do
        for j = #block, 1, -1 do
            table.insert(result, block[j])
        end
    end
    
    return result
end

-- Calculate simple checksum
function StringCrypto:calculateChecksum(data)
    local sum = 0
    for i, byte in ipairs(data) do
        sum = sum + byte * i
    end
    return sum % 65536
end

-- Generate encrypted string table
function StringCrypto:encryptStringTable(strings, masterKey)
    local encryptedTable = {}
    local decoyCount = math.random(50, 100)
    
    -- Add real encrypted strings
    for i, str in ipairs(strings) do
        encryptedTable[i] = self:encrypt(str, masterKey .. tostring(i))
    end
    
    -- Add decoy entries
    local decoys = generateDecoyStrings(decoyCount)
    for i, decoy in ipairs(decoys) do
        local fakeIndex = #strings + i
        encryptedTable[fakeIndex] = self:encrypt(decoy, masterKey .. tostring(fakeIndex))
    end
    
    return encryptedTable
end

-- Anti-tampering trap
function StringCrypto:createTamperTrap()
    return function()
        -- This function is designed to crash if someone tries to analyze it
        local x = nil
        local function recursive(n)
            if n <= 0 then
                return x.nonexistent.field
            end
            return recursive(n - 1) + recursive(n - 1)
        end
        return recursive(1000)
    end
end

-- Generate decryption function with traps
function StringCrypto:generateDecryptor(masterKey)
    local trap = self:createTamperTrap()
    
    return function(encryptedData, index)
        -- Anti-debugging check
        local start = os.clock()
        for i = 1, 1000 do
            math.sin(i)
        end
        if os.clock() - start > 0.1 then
            trap() -- Trigger trap if execution is too slow (debugger)
        end
        
        -- Decrypt the string
        return StringCrypto:decrypt(encryptedData, masterKey .. tostring(index))
    end
end

function StringCrypto:new()
    return setmetatable({}, StringCrypto)
end

return StringCrypto
