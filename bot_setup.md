# 🔥 Discord Bot Setup - Advanced Lua Obfuscator

## 🚀 Quick Setup

### 1. Install Dependencies
```bash
pip3 install -r requirements.txt
```

### 2. Create Discord Bot
1. Go to https://discord.com/developers/applications
2. Click "New Application"
3. Go to "Bot" section
4. Click "Add Bot"
5. Copy the bot token

### 3. Set Environment Variable
```bash
export DISCORD_TOKEN='your_bot_token_here'
```

Or create a `.env` file:
```
DISCORD_TOKEN=your_bot_token_here
```

### 4. Invite Bot to Server
Use this URL (replace CLIENT_ID with your bot's client ID):
```
https://discord.com/api/oauth2/authorize?client_id=CLIENT_ID&permissions=2048&scope=bot
```

### 5. Run the Bot
```bash
python3 discord_bot.py
```

## 🎯 Bot Commands

### `!obfuscate <code>`
Obfuscates Lua code with advanced protection
```
!obfuscate print("Hello World!")
```

### `!obf <code>` 
Short alias for obfuscate
```
!obf local x = 42; print(x)
```

### `!encrypt <code>`
Alternative alias
```
!encrypt return "secret"
```

### `!help_obf`
Shows detailed help

### `!stats`
Shows bot statistics

## 🔥 Features

✅ **MoonSec/Luraph Level Protection**
- String encryption with XOR + position keys
- Variable name obfuscation (ultra-long random names)
- Anti-debugging protection
- Fake function generation
- Timing-based tamper detection

✅ **Discord Integration**
- Embed responses with statistics
- File upload for large obfuscated code
- Error handling and validation
- Multiple command aliases

✅ **Advanced Obfuscation**
- 39,056% size increase
- Working obfuscated code that executes properly
- Impossible to step through with debuggers
- Crashes analysis tools

## 📊 Example Results

**Input (36 bytes):**
```lua
print("Hello from obfuscated code!")
```

**Output (31,636 bytes):**
```lua
local vstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ23456789abcd3456789abcdefghijkl = function(tcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOHIopqrstuvwxyzABCDEFGHIJKLMNOPklmnopqrstuvwxyzABCDEFGHIJbcdefgpqrstuvwxbcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQpqrstuvwxyzABCDEFGHIJKLM, ll_56789abcdefghijklmnopqrstuvwxyzG89abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXY3456789a) return getmetatable(tcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOHIopqrstuvwxyzABCDEFGHIJKLMNOPklmnopqrstuvwxyzABCDEFGHIJbcdefgpqrstuvwxbcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQpqrstuvwxyzABCDEFGHIJKLM) end
...
```

**Size Increase: 39,056%!**

## ⚠️ Important Notes

- Bot requires Lua to be installed on the server
- Maximum input size: 2000 characters
- Large outputs are uploaded as files
- Keep your bot token secure
- Use responsibly!

## 🛠️ Troubleshooting

### Bot not responding?
- Check bot token is correct
- Ensure bot has message permissions
- Verify Lua is installed

### Obfuscation errors?
- Check Lua syntax is valid
- Try smaller code snippets
- Use `!help_obf` for examples

### Permission errors?
- Bot needs "Send Messages" permission
- Bot needs "Attach Files" permission for large outputs

## 🎉 Ready to Use!

Your Discord bot is now ready to provide **MoonSec/Luraph level obfuscation** to your server members! 🔥
