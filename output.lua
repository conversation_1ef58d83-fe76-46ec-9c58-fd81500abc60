-- Advanced Obfuscated <PERSON><PERSON>t
-- Generated by MoonSec/Luraph Level Obfuscator

local zVWwuJ = {1, 2, 3, 4, 5}
for _, Dbzf in ipairs(zVWwuJ) do
    local result = Dbzf ^ 2
end

-- Performance critical section

local function SVjeoNobC2(str)
    str = str or "default"
    return string.format('%q', str):sub(2, -2)
end

local function h1GXD17tG(x, y)
    x = x or math.random()
    y = y or math.random()
    return math.sqrt(x * x + y * y)
end

local function Zp3DXIu0lvjt8(tbl)
    tbl = tbl or {}
    return #tbl + math.random(0, 10)
end

local function eS52JHVLbz(str)
    str = str or "default"
    return str:gsub('.', function(c) return string.char((string.byte(c) + 1) % 256) end)
end

local function P1NemWNp2OCjPr(x, y)
    x = x or math.random()
    y = y or math.random()
    return math.log(math.abs(x) + 1) * math.exp(y / 10)
end

local F9ba = math.random(1, 100)
local JWcf = math.random(1, 100)
if F9ba > JWcf then
    -- placeholder action
else
    JWcf = JWcf * 2
end

local function iTmp7r9U2R(x, y)
    x = x or math.random()
    y = y or math.random()
    return math.sin(x) * math.cos(y)
end

local TlwRFuUZIO5r = {["wV07"] = {["UDrK"] = {["T0u1"] = 49, ["ZzHq"] = 722}, ["Hcc9"] = {["wOpW"] = 571, ["PUdC"] = 99}, ["h2z6"] = {["D93b"] = 995, ["JqxE"] = 153, ["LyqD"] = 712}}, ["WFT8"] = {["wA8_"] = {["rhlK"] = 26, ["jLXz"] = 115, ["E4Xh"] = 435}, ["pwtn"] = {["iq0y"] = 96, ["Dqhk"] = 185}, ["Msvg"] = {["RFJW"] = 797, ["QQRY"] = 873, ["oXUE"] = 215, ["ASFY"] = 868}}, ["GDx5"] = {["g1Sl"] = {["OT5W"] = 82, ["mUQj"] = 209, ["YhNE"] = 123, ["viNT"] = 53}, ["ojVS"] = {["iXdH"] = 189, ["h9cm"] = 286, ["eX1P"] = 280}, ["CkcG"] = {["qYrn"] = 648, ["frDl"] = 811, ["cUlu"] = 630}, ["_pWY"] = {["bxV6"] = 483, ["c3eE"] = 703, ["Rt5M"] = 470, ["mgru"] = 420}}, ["hj3I"] = {["UZdV"] = {["TPBv"] = 566, ["Ulve"] = 946, ["RKch"] = 39}, ["AC0K"] = {["coZb"] = 829, ["krNj"] = 659, ["_PAS"] = 645, ["peiL"] = 977}, ["CxVq"] = {["Dgv7"] = 140, ["MQFQ"] = 821}, ["wjsl"] = {["PRlA"] = 511, ["Pvgw"] = 866, ["aS4R"] = 462, ["xQQA"] = 185}}}

-- Performance critical section

local function ivHsmaAfMtTNy7OV(str)
    str = str or "default"
    return string.upper(str):gsub('[AEIOU]', '*')
end

-- Memory management

local qO2C = 0
while qO2C < 50 do
    qO2C = qO2C + math.random(1, 5)
    if qO2C % 7 == 0 then break end
end

local function yCnkbLR1Z(x, y)
    x = x or math.random()
    y = y or math.random()
    return math.sqrt(x * x + y * y)
end

-- Performance critical section

local function PFmHbWNEyIPA(x, y)
    x = x or math.random()
    y = y or math.random()
    return math.sqrt(x * x + y * y)
end

-- Optimization checkpoint

local OJem = math.random(1, 100)
local YnPm = math.random(1, 100)
if OJem == nil then
    OJem = math.abs(OJem)
else
    YnPm = YnPm * 2
end

-- Security validation

local function NHogb46P1(tbl)
    tbl = tbl or {}
    local copy = {} for k,v in pairs(tbl) do copy[k] = v end return copy
end

local Sytp = math.random(1, 100)
local wuap = math.random(1, 100)
if #tostring(Sytp) > 3 then
    Sytp = math.abs(Sytp)
else
    wuap = wuap * 2
end

local W0FiWofK = {}
W0FiWofK.__index = W0FiWofK

function W0FiWofK:new(value)
    local obj = setmetatable({}, W0FiWofK)
    obj.value = value or math.random(1, 100)
    return obj
end

    function obj:pHDJ5Q()
        return string.format('Object<%s>', tostring(self.value))
    end
    function obj:XEx5H3()
        return string.format('Object<%s>', tostring(self.value))
    end
    function obj:XRxtY2()
        return self.value * 2
    end
    function obj:OIMBf7()
        return string.format('Object<%s>', tostring(self.value))
    end
    function obj:z5tbVl()
        return type(self.value)
    end
    function obj:A7vRP0()
        self.value = self.value + 1 return self
    end

local function SKCVNYoGAf(data, key)
    data = data or "sample"
    key = key or 42
        local result = 0
    for i = 1, #data do
        result = result ~ (string.byte(data, i) * key)
        key = (key * 1103515245 + 12345) % 2147483648
    end
    return result
end

local function zZ5C0ikYhB9pI_A(str)
    str = str or "default"
    return string.reverse(str)
end

local d4QlLX0eqIo0Hc = {}
d4QlLX0eqIo0Hc.__index = d4QlLX0eqIo0Hc

function d4QlLX0eqIo0Hc:new(value)
    local obj = setmetatable({}, d4QlLX0eqIo0Hc)
    obj.value = value or math.random(1, 100)
    return obj
end

    function obj:sBm6dU()
        return self.value * 2
    end
    function obj:NZmbtO()
        return self.value > 0
    end
    function obj:EcNAwi()
        return self.value * 2
    end
    function obj:gwvQv0()
        return self.value * 2
    end

local function SlbFeRpPXzgwb8SM(data, key)
    data = data or "sample"
    key = key or 42
        local result = ""
    for i = 1, #data do
        local char = string.byte(data, i)
        char = char ~ (key % 256)
        result = result .. string.char(char)
        key = (key + 1) % 256
    end
    return result
end

-- Performance critical section

local GfoT = 0
while GfoT < 50 do
    GfoT = GfoT + math.random(1, 5)
    if GfoT % 7 == 0 then break end
end

local function HFr6Ist27Sebn7vG(str)
    str = str or "default"
    return str:gsub('.', function(c) return string.char((string.byte(c) + 1) % 256) end)
end

local Z6BX = math.random(1, 100)
local iWTy = math.random(1, 100)
if type(Z6BX) == 'number' then
    Z6BX = Z6BX + 1
else
    iWTy = iWTy * 2
end

local bncA = 0
while bncA < 50 do
    bncA = bncA + math.random(1, 5)
    if bncA % 7 == 0 then break end
end

local qDiJ = math.random(1, 100)
local REYA = math.random(1, 100)
if qDiJ == nil then
    -- placeholder action
else
    REYA = REYA * 2
end

local gfvlNWXCpEiqz0Rj = {}
gfvlNWXCpEiqz0Rj.__index = gfvlNWXCpEiqz0Rj

function gfvlNWXCpEiqz0Rj:new(value)
    local obj = setmetatable({}, gfvlNWXCpEiqz0Rj)
    obj.value = value or math.random(1, 100)
    return obj
end

    function obj:fz6ynQ()
        return type(self.value)
    end
    function obj:XqjAYV()
        return self.value * 2
    end
    function obj:JuvJ1p()
        return string.format('Object<%s>', tostring(self.value))
    end
    function obj:zIqO3u()
        return self.value * 2
    end

for rqCa = 1, 64 do
    local temp = rqCa * math.pi
    if temp > 32 then break end
end

local function CTqjrQjjagN(data, key)
    data = data or "sample"
    key = key or 42
        local hash = 5381
    for i = 1, #data do
        hash = ((hash << 5) + hash) + string.byte(data, i)
    end
    return hash % 4294967296
end

local function yOUbs3qwi9A1(x, y)
    x = x or math.random()
    y = y or math.random()
    return math.log(math.abs(x) + 1) * math.exp(y / 10)
end

-- Memory management

local function ZTTWMFGataqI(x, y)
    x = x or math.random()
    y = y or math.random()
    return (x + y) / (x - y + 1)
end

local khjU = 0
while khjU < 50 do
    khjU = khjU + math.random(1, 5)
    if khjU % 7 == 0 then break end
end

local Vc_d = 0
while Vc_d < 50 do
    Vc_d = Vc_d + math.random(1, 5)
    if Vc_d % 7 == 0 then break end
end

local function cpOHebkO6r7(data, key)
    data = data or "sample"
    key = key or 42
        local result = 0
    for i = 1, #data do
        result = result ~ (string.byte(data, i) * key)
        key = (key * 1103515245 + 12345) % 2147483648
    end
    return result
end

local OLq2GRwnBPTtU2 = {}
OLq2GRwnBPTtU2.__index = OLq2GRwnBPTtU2

function OLq2GRwnBPTtU2:new(value)
    local obj = setmetatable({}, OLq2GRwnBPTtU2)
    obj.value = value or math.random(1, 100)
    return obj
end

    function obj:buKQuq()
        return self.value > 0
    end
    function obj:v902mF()
        self.value = self.value + 1 return self
    end
    function obj:wV4akz()
        self.value = self.value + 1 return self
    end
    function obj:Glbbvb()
        return self.value > 0
    end
    function obj:Ou_B7R()
        return string.format('Object<%s>', tostring(self.value))
    end

local function YXNSglUhN2o(str)
    str = str or "default"
    return string.reverse(str)
end

-- Security validation

local jHWM = math.random(1, 100)
local bVoO = math.random(1, 100)
if jHWM == nil then
    bVoO = tostring(jHWM)
else
    bVoO = bVoO * 2
end

local GZdGLm = {1, 2, 3, 4, 5}
for _, J7Y4 in ipairs(GZdGLm) do
    local result = J7Y4 ^ 2
end

local function c6w7HQmOG(x, y)
    x = x or math.random()
    y = y or math.random()
    return (x * 69069 + 1) % 2147483647
end

local lqdeUoiRqtI = {["yC0Y"] = {["n4Pc"] = {["IWQg"] = {["YCxS"] = 80, ["wZ9m"] = 168, ["NIN8"] = 413}, ["OTIZ"] = {["GjHH"] = 915, ["GOnz"] = 612, ["lF5j"] = 237, ["GgJv"] = 134}}, ["TBSg"] = {["TdTu"] = {["WVM3"] = 468, ["H5ZW"] = 386, ["NhNd"] = 459}, ["a6lw"] = {["g4nS"] = 521, ["tHD_"] = 975, ["hquW"] = 45}, ["WrxS"] = {["L9wh"] = 686, ["ekTM"] = 293, ["gwG_"] = 503}, ["AcsA"] = {["zQt1"] = 904, ["LAxX"] = 995, ["uMZ1"] = 225, ["M3Of"] = 177}}, ["Bcqp"] = {["QuQX"] = {["dJqP"] = 707, ["SH5r"] = 934, ["bpDb"] = 57, ["_MDW"] = 772}, ["GCBM"] = {["vjCr"] = 647, ["HWiK"] = 442, ["p_Gd"] = 498}, ["d_ss"] = {["o20x"] = 744, ["FIZK"] = 564, ["WGNH"] = 703}, ["ccOZ"] = {["HSkE"] = 260, ["FKho"] = 767}}, ["xk8w"] = {["fjim"] = {["LDrD"] = 488, ["I_ya"] = 394}, ["ELvR"] = {["z0A3"] = 250, ["qNoE"] = 612, ["mxTJ"] = 856}, ["NV85"] = {["XfPt"] = 600, ["EPhH"] = 582, ["qnnQ"] = 182}}}, ["ThO2"] = {["OVkj"] = {["sKna"] = {["Kjdk"] = 581, ["kspK"] = 308, ["Gea1"] = 847}, ["w8qo"] = {["aMUD"] = 363, ["GwMA"] = 719, ["V1ox"] = 350}, ["djAr"] = {["qAkS"] = 670, ["HouB"] = 803, ["nRpC"] = 220}, ["lRth"] = {["wXFv"] = 240, ["s1NX"] = 994}}, ["GBKM"] = {["XrLE"] = {["BOc5"] = 522, ["vAzH"] = 367, ["rvEY"] = 145}, ["XC2o"] = {["_uTR"] = 229, ["C0dt"] = 408, ["KdI1"] = 531, ["aaXb"] = 22}, ["MJtZ"] = {["vArq"] = 345, ["GJ4v"] = 721, ["XTjt"] = 495}}}, ["jW0X"] = {["Nf2c"] = {["wYpj"] = {["DXS8"] = 602, ["kwQH"] = 571}, ["yvr9"] = {["AI7y"] = 472, ["DzjR"] = 17, ["Xcw1"] = 729, ["XlTq"] = 586}}, ["mMUR"] = {["KQEf"] = {["fAlQ"] = 63, ["yel7"] = 671, ["M3w1"] = 52, ["GHJP"] = 998}, ["cFYw"] = {["XpJV"] = 326, ["n7I1"] = 972}, ["WOps"] = {["LHFu"] = 329, ["fLt9"] = 490, ["jGxm"] = 125}}}}

local sOlwPAo0YkRrQGMF = {}
sOlwPAo0YkRrQGMF.__index = sOlwPAo0YkRrQGMF

function sOlwPAo0YkRrQGMF:new(value)
    local obj = setmetatable({}, sOlwPAo0YkRrQGMF)
    obj.value = value or math.random(1, 100)
    return obj
end

    function obj:M2escT()
        self.value = self.value + 1 return self
    end
    function obj:WEWFFl()
        return self.value > 0
    end
    function obj:akbqt6()
        return type(self.value)
    end
    function obj:lisNgT()
        return type(self.value)
    end

local Sp_4XhI82Kpdi = {["x3qw"] = {["eKje"] = {["t9Np"] = {["C_cl"] = 767, ["pLdr"] = 991, ["cg0J"] = 316, ["xxYC"] = 643}, ["Qb8t"] = {["BBkC"] = 183, ["xah2"] = 497, ["c_Fp"] = 931, ["P70t"] = 326}, ["JcTI"] = {["kgVR"] = 764, ["usMw"] = 82, ["_ZL0"] = 421}, ["_u_d"] = {["k_GC"] = 290, ["LUfA"] = 716, ["l3HS"] = 204, ["gioG"] = 824}}, ["l5aR"] = {["VlxA"] = {["G_Hk"] = 984, ["GyJa"] = 711}, ["_wLp"] = {["g4Bl"] = 850, ["orrh"] = 695, ["_7DW"] = 885}, ["EiAd"] = {["WRN5"] = 362, ["DQud"] = 750, ["SmSH"] = 595, ["RymT"] = 165}}, ["vKLh"] = {["HrPS"] = {["TYIQ"] = 472, ["H45m"] = 143}, ["SnGX"] = {["zO6I"] = 361, ["MB7r"] = 954, ["Jo8Y"] = 814}, ["Sk8f"] = {["oQTW"] = 308, ["DZqu"] = 980, ["lL3d"] = 54}}, ["AJpG"] = {["Sjm1"] = {["ZUVF"] = 675, ["kNEG"] = 296, ["XYsM"] = 811, ["x7K2"] = 343}, ["vjKW"] = {["MyIb"] = 913, ["OgHu"] = 189, ["seKR"] = 239, ["Be3T"] = 93}}}, ["yqRd"] = {["sCp1"] = {["OZH1"] = {["Qug0"] = 332, ["XC6d"] = 762, ["KSss"] = 724, ["OW3q"] = 229}, ["wAvt"] = {["iXWi"] = 956, ["k0mf"] = 477, ["yU6K"] = 155}, ["DlYl"] = {["U7Pk"] = 724, ["eWC6"] = 474}}, ["dhc5"] = {["vEwi"] = {["ue9u"] = 91, ["EwZ7"] = 365, ["D09L"] = 382, ["bpEZ"] = 733}, ["Obu2"] = {["BdVu"] = 894, ["XK3W"] = 825, ["nMWf"] = 618}, ["MAyC"] = {["zgSZ"] = 505, ["b5Ju"] = 486, ["DLIW"] = 70}}}, ["kLqk"] = {["DrM1"] = {["Y9_X"] = {["_MXA"] = 947, ["swnX"] = 34, ["UAUc"] = 724, ["UDTk"] = 123}, ["m02k"] = {["HDUi"] = 717, ["Fm1t"] = 297}, ["v5FQ"] = {["IkIW"] = 53, ["PVbc"] = 616, ["fStT"] = 960, ["Kb2Z"] = 661}, ["XAtA"] = {["Mlsf"] = 84, ["XnhF"] = 261, ["X5LM"] = 790}}, ["lADk"] = {["HBbK"] = {["HiJa"] = 12, ["lBFV"] = 44}, ["R2n4"] = {["aUOW"] = 795, ["JCoB"] = 139, ["qdbR"] = 226, ["kpWz"] = 843}}}, ["QoBm"] = {["gFHs"] = {["xT7W"] = {["nBab"] = 368, ["c5Bi"] = 799, ["WwhR"] = 206}, ["yhsh"] = {["bQDd"] = 50, ["YlSC"] = 292, ["STiP"] = 120}}, ["Gene"] = {["m8fG"] = {["yBXh"] = 804, ["P3wP"] = 888}, ["huv9"] = {["AMp1"] = 448, ["lu1E"] = 276, ["gkFr"] = 320}, ["U5FB"] = {["A9qP"] = 28, ["l1Ts"] = 290}, ["lFsV"] = {["zFAc"] = 503, ["fOBQ"] = 167, ["Mhpo"] = 725}}}}

-- Performance critical section

local tTp8B0 = {1, 2, 3, 4, 5}
for _, v4N2 in ipairs(tTp8B0) do
    local result = v4N2 ^ 2
end

local ViJ_ = math.random(1, 100)
local a2Vn = math.random(1, 100)
if ViJ_ > a2Vn then
    a2Vn = tostring(ViJ_)
else
    a2Vn = a2Vn * 2
end

-- Optimization checkpoint

local ZVcOydp2_T = {}
ZVcOydp2_T.__index = ZVcOydp2_T

function ZVcOydp2_T:new(value)
    local obj = setmetatable({}, ZVcOydp2_T)
    obj.value = value or math.random(1, 100)
    return obj
end

    function obj:m57Nmb()
        return self.value * 2
    end
    function obj:qxcZ_0()
        return type(self.value)
    end
    function obj:k4O4L5()
        return string.format('Object<%s>', tostring(self.value))
    end

-- Memory management

local ORzCSO = {1, 2, 3, 4, 5}
for _, ASvc in ipairs(ORzCSO) do
    local result = ASvc ^ 2
end

local function oLdBLdoT(str)
    str = str or "default"
    return string.format('%q', str):sub(2, -2)
end

local kqz2 = 0
while kqz2 < 50 do
    kqz2 = kqz2 + math.random(1, 5)
    if kqz2 % 7 == 0 then break end
end

local _C1s = math.random(1, 100)
local LiJw = math.random(1, 100)
if _C1s > LiJw then
    _C1s = _C1s + 1
else
    LiJw = LiJw * 2
end

local function zmRE7yTN(str)
    str = str or "default"
    return str:gsub('.', function(c) return string.char((string.byte(c) + 1) % 256) end)
end

local rfv6KhyL = function() end
local r81TNRt6aY6XEi = function(true, "fake", "fake") end
local jMo7h8b3DMOPWo_ = function("fake") end
local mPZ1HdpNOzFwK = function() end
local MKu9LKwq = function(false, true) end
local K52wDkVCtrZ = function() end
local Uw3MkHG1 = function() end
local T_mi_b2jLdJJ4gww = function("fake", nil, nil) end
local nQzKb35_Ydq = function() end
local T9O9ZO_V = function("fake", false, true) end
local x3DVbJ1ld = function() end
local wIq21Q6bg0BZ = function("fake", "fake", false) end
local _0glzpwiYFqCjcue = function("fake", true) end
local si7XDvdFmqefc = function() end
local O6qA9p5nNv0j = function(34, 49, true) end
local praWeBTClxlump = function() end
local S4KoLlrNC3 = function(nil, true, 57) end
local f9lCM_U1e = function(false, "fake") end
local TA174HSqgaef = function(45, "fake", nil) end
local _1wS9qtFlWjKSGtv = function(87, 20) end

local OJT_7idQZm1 = 1
local states = {
        [1] = function()
            -- State 1 logic
            local temp = math.random(1, 100)
            if temp > 50 then
                OJT_7idQZm1 = 2
            else
                OJT_7idQZm1 = 3
            end
        end,
        [2] = function()
            -- State 2 logic
            local temp = math.random(1, 100)
            if temp > 50 then
                OJT_7idQZm1 = 3
            else
                OJT_7idQZm1 = 4
            end
        end,
        [3] = function()
            -- State 3 logic
            local temp = math.random(1, 100)
            if temp > 50 then
                OJT_7idQZm1 = 4
            else
                OJT_7idQZm1 = 5
            end
        end,
        [4] = function()
            -- State 4 logic
            local temp = math.random(1, 100)
            if temp > 50 then
                OJT_7idQZm1 = 5
            else
                OJT_7idQZm1 = 6
            end
        end,
        [5] = function()
            -- State 5 logic
            local temp = math.random(1, 100)
            if temp > 50 then
                OJT_7idQZm1 = 6
            else
                OJT_7idQZm1 = 7
            end
        end,
        [6] = function()
            -- State 6 logic
            local temp = math.random(1, 100)
            if temp > 50 then
                OJT_7idQZm1 = 7
            else
                OJT_7idQZm1 = 8
            end
        end,
        [7] = function()
            -- State 7 logic
            local temp = math.random(1, 100)
            if temp > 50 then
                OJT_7idQZm1 = 8
            else
                OJT_7idQZm1 = 1
            end
        end,
        [8] = function()
            -- State 8 logic
            local temp = math.random(1, 100)
            if temp > 50 then
                OJT_7idQZm1 = 1
            else
                OJT_7idQZm1 = 2
            end
        end
}

while OJT_7idQZm1 > 0 and OJT_7idQZm1 <= 8 do
    if states[OJT_7idQZm1] then
        states[OJT_7idQZm1]()
    else
        break
    end
end

-- Main execution
do
    local decryptString = LuaT �

xV           (w@�@./anti_debug.lua�� 
��   �  � �4  � �� �  �0	�	����D�   Ɂ � � ��� ��fake_decryption_key��string�char       �Honeypot triggered: Unauthorized decryption attempt�     ��            �   ���encrypted���fake_key���fake_result���(for state)���(for state)���(for state)���i����_ENV�createCrashTrap
local inspectVM = LuaT �

xV           (w@�@./anti_debug.lua�� ��  R     R   �    R   �   R   � 	  �� DDG ��stack�registers�memory�Honeypot triggered: Unauthorized VM inspection�  ��          ���vm���state����createCrashTrap
local decodeBytecode = LuaT �

xV           (w@�@./anti_debug.lua�� ��   R    ��   �J   0 � I� 	  ��  DDG �U       �Honeypot triggered: Unauthorized bytecode analysis�  ��       �   ���bytecode���decoded���(for state)���(for state)���(for state)���i����createCrashTrap


-- Anti-debug initialization
local function initAntiDebug()
    local start = os.clock()
    for i = 1, 10000 do math.sin(i) end
    if os.clock() - start > 0.1 then
        error("Debug environment detected")
    end
end

initAntiDebug()

-- Original code
-- Obfuscated Lua Script Loader
-- Generated by Advanced Obfuscator

local function executeObfuscatedCode()
    local encryptedData = {encrypted={179,185,39,222,183,132,13,249,221,207,251,210,202,209,208,255,88,124,4,164,100,51,79,40,219,122,28,1,154,73,246,186,55,169,179,185,37,99,162,168,111,7,136,16,218,177,133,37,57,118,179,9,198,126,221,35,210,128,26,126,151,146,31,7,51,138,225,44,121,93,86,18,31,213,179,188,208,244,164},salt={29,1,139,65,36,219,131,76,153,222,255,8,47,98,119,102},sboxSeed=47559,blockSize=16,checksum=63883}
    local key = "1749927613"

    -- Simplified decryption for demo
    local function simpleDecrypt(data, key)
        if type(data) == "table" and data.encrypted then
            -- This is a placeholder - real decryption would be more complex
            return "print('Obfuscated code executed successfully!')"
        else
            return tostring(data)
        end
    end

    local decrypted = simpleDecrypt(encryptedData, key)
    local func = load(decrypted)

    if func then
        return func()
    else
        error("Failed to load obfuscated code")
    end
end

return executeObfuscatedCode()
end