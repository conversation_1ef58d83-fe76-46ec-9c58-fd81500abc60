--[[
    Runtime Loader
    Loads and executes obfuscated code with VM integration
]]

local Loader = {}
Loader.__index = Loader

-- Import required modules (these would be embedded in final output)
local VMCore = require("vm_core")
local StringCrypto = require("string_crypto")
local AntiDebug = require("anti_debug")

-- Loader configuration
local LOADER_CONFIG = {
    MAGIC_HEADER = 0x4C554142, -- "LUAB" in hex
    VERSION = 1,
    ENCRYPTION_LAYERS = 3,
    CHECKSUM_ALGORITHM = "CRC32",
    COMPRESSION_ENABLED = true
}

-- Simple compression using run-length encoding
local function compress(data)
    local compressed = {}
    local i = 1
    
    while i <= #data do
        local current = data[i]
        local count = 1
        
        -- Count consecutive identical bytes
        while i + count <= #data and data[i + count] == current and count < 255 do
            count = count + 1
        end
        
        if count > 3 then
            -- Use RLE for runs of 4 or more
            table.insert(compressed, 0xFF) -- Escape byte
            table.insert(compressed, count)
            table.insert(compressed, current)
        else
            -- Store literally
            for j = 0, count - 1 do
                table.insert(compressed, current)
            end
        end
        
        i = i + count
    end
    
    return compressed
end

-- Decompress run-length encoded data
local function decompress(compressed)
    local data = {}
    local i = 1
    
    while i <= #compressed do
        if compressed[i] == 0xFF and i + 2 <= #compressed then
            -- RLE sequence
            local count = compressed[i + 1]
            local value = compressed[i + 2]
            for j = 1, count do
                table.insert(data, value)
            end
            i = i + 3
        else
            -- Literal byte
            table.insert(data, compressed[i])
            i = i + 1
        end
    end
    
    return data
end

-- Calculate CRC32 checksum
local function calculateCRC32(data)
    local crc = 0xFFFFFFFF
    local crcTable = {}
    
    -- Generate CRC table
    for i = 0, 255 do
        local c = i
        for j = 1, 8 do
            if c & 1 == 1 then
                c = 0xEDB88320 ~ (c >> 1)
            else
                c = c >> 1
            end
        end
        crcTable[i] = c
    end
    
    -- Calculate CRC
    for i = 1, #data do
        local byte = data[i]
        crc = crcTable[(crc ~ byte) & 0xFF] ~ (crc >> 8)
    end
    
    return crc ~ 0xFFFFFFFF
end

-- Pack bytecode with metadata
function Loader:packBytecode(instructions, constants, stringTable, metadata)
    local packed = {}
    
    -- Header
    table.insert(packed, LOADER_CONFIG.MAGIC_HEADER)
    table.insert(packed, LOADER_CONFIG.VERSION)
    table.insert(packed, #instructions)
    table.insert(packed, #constants)
    table.insert(packed, #stringTable)
    
    -- Instructions
    for _, instruction in ipairs(instructions) do
        table.insert(packed, instruction)
    end
    
    -- Constants
    for _, constant in ipairs(constants) do
        if type(constant) == "string" then
            table.insert(packed, 1) -- String type marker
            table.insert(packed, #constant)
            for i = 1, #constant do
                table.insert(packed, string.byte(constant, i))
            end
        elseif type(constant) == "number" then
            table.insert(packed, 2) -- Number type marker
            -- Convert number to bytes (simplified)
            local bytes = {string.byte(string.pack("d", constant), 1, 8)}
            for _, byte in ipairs(bytes) do
                table.insert(packed, byte)
            end
        elseif type(constant) == "boolean" then
            table.insert(packed, 3) -- Boolean type marker
            table.insert(packed, constant and 1 or 0)
        else
            table.insert(packed, 0) -- Nil type marker
        end
    end
    
    -- String table
    for _, str in ipairs(stringTable) do
        table.insert(packed, #str)
        for i = 1, #str do
            table.insert(packed, string.byte(str, i))
        end
    end
    
    -- Metadata
    if metadata then
        local metaStr = string.format("%q", metadata)
        table.insert(packed, #metaStr)
        for i = 1, #metaStr do
            table.insert(packed, string.byte(metaStr, i))
        end
    else
        table.insert(packed, 0)
    end
    
    return packed
end

-- Unpack bytecode
function Loader:unpackBytecode(packed)
    local pos = 1
    
    -- Verify header
    if packed[pos] ~= LOADER_CONFIG.MAGIC_HEADER then
        error("Invalid bytecode header")
    end
    pos = pos + 1
    
    local version = packed[pos]
    if version ~= LOADER_CONFIG.VERSION then
        error("Unsupported bytecode version: " .. version)
    end
    pos = pos + 1
    
    -- Read counts
    local instructionCount = packed[pos]; pos = pos + 1
    local constantCount = packed[pos]; pos = pos + 1
    local stringCount = packed[pos]; pos = pos + 1
    
    -- Read instructions
    local instructions = {}
    for i = 1, instructionCount do
        instructions[i] = packed[pos]
        pos = pos + 1
    end
    
    -- Read constants
    local constants = {}
    for i = 1, constantCount do
        local typeMarker = packed[pos]; pos = pos + 1
        
        if typeMarker == 1 then -- String
            local length = packed[pos]; pos = pos + 1
            local str = ""
            for j = 1, length do
                str = str .. string.char(packed[pos])
                pos = pos + 1
            end
            constants[i] = str
        elseif typeMarker == 2 then -- Number
            local bytes = {}
            for j = 1, 8 do
                bytes[j] = packed[pos]
                pos = pos + 1
            end
            local unpack = unpack or table.unpack
            constants[i] = string.unpack("d", string.char(unpack(bytes)))
        elseif typeMarker == 3 then -- Boolean
            constants[i] = packed[pos] == 1
            pos = pos + 1
        else -- Nil
            constants[i] = nil
        end
    end
    
    -- Read string table
    local stringTable = {}
    for i = 1, stringCount do
        local length = packed[pos]; pos = pos + 1
        local str = ""
        for j = 1, length do
            str = str .. string.char(packed[pos])
            pos = pos + 1
        end
        stringTable[i] = str
    end
    
    -- Read metadata
    local metadataLength = packed[pos]; pos = pos + 1
    local metadata = nil
    if metadataLength > 0 then
        local metaStr = ""
        for i = 1, metadataLength do
            metaStr = metaStr .. string.char(packed[pos])
            pos = pos + 1
        end
        metadata = load("return " .. metaStr)()
    end
    
    return instructions, constants, stringTable, metadata
end

-- Create secure execution environment
function Loader:createSecureEnvironment()
    local env = {}
    
    -- Copy safe globals
    local safeGlobals = {
        "print", "type", "tostring", "tonumber", "pairs", "ipairs", "next",
        "getmetatable", "setmetatable", "rawget", "rawset", "rawlen",
        "string", "table", "math", "os", "coroutine"
    }
    
    for _, global in ipairs(safeGlobals) do
        env[global] = _G[global]
    end
    
    -- Add Roblox globals if available
    if _G.game then
        env.game = _G.game
        env.workspace = _G.workspace
        env.script = _G.script
        env.wait = _G.wait
        env.spawn = _G.spawn
        env.delay = _G.delay
    end
    
    -- Restricted functions
    env.loadstring = nil
    env.load = nil
    env.dofile = nil
    env.loadfile = nil
    env.debug = nil
    env.getfenv = nil
    env.setfenv = nil
    
    return env
end

-- Main loader function
function Loader:load(encryptedBytecode, masterKey, antiDebugEnabled)
    antiDebugEnabled = antiDebugEnabled ~= false -- Default to true
    
    -- Initialize anti-debug protection
    local antiDebug = nil
    if antiDebugEnabled then
        antiDebug = AntiDebug:initialize()
        antiDebug:startMonitoring()
    end
    
    -- Decrypt bytecode
    local stringCrypto = StringCrypto:new()
    local decryptedData = stringCrypto:decrypt(encryptedBytecode, masterKey)
    
    -- Decompress if needed
    local bytecodeData = decryptedData
    if LOADER_CONFIG.COMPRESSION_ENABLED then
        bytecodeData = decompress(bytecodeData)
    end
    
    -- Verify checksum
    local expectedChecksum = bytecodeData[#bytecodeData]
    bytecodeData[#bytecodeData] = nil -- Remove checksum from data
    local actualChecksum = calculateCRC32(bytecodeData) & 0xFFFF
    
    if actualChecksum ~= expectedChecksum then
        error("Bytecode integrity check failed")
    end
    
    -- Unpack bytecode
    local instructions, constants, stringTable, metadata = self:unpackBytecode(bytecodeData)
    
    -- Create VM instance
    local vm = VMCore:new()
    
    -- Create secure environment
    local env = self:createSecureEnvironment()
    
    -- Set up VM environment
    for k, v in pairs(env) do
        vm.globals[k] = v
    end
    
    -- Execute with monitoring
    local function executeWithMonitoring()
        if antiDebug then
            antiDebug:periodicCheck()
        end
        
        return vm:execute(instructions, constants)
    end
    
    -- Wrap execution in protected call
    local success, result = pcall(executeWithMonitoring)
    
    if not success then
        if antiDebugEnabled then
            -- Could be anti-debug trigger, handle appropriately
            error("Execution failed: Security violation detected")
        else
            error("Execution failed: " .. tostring(result))
        end
    end
    
    return result
end

-- Generate loader stub for embedding
function Loader:generateLoaderStub(encryptedBytecode, masterKey)
    -- Convert encrypted data to a string representation
    local dataStr = "{"
    if encryptedBytecode.encrypted then
        dataStr = dataStr .. "encrypted={"
        for i, byte in ipairs(encryptedBytecode.encrypted) do
            dataStr = dataStr .. tostring(byte)
            if i < #encryptedBytecode.encrypted then dataStr = dataStr .. "," end
        end
        dataStr = dataStr .. "},"

        dataStr = dataStr .. "salt={"
        for i, byte in ipairs(encryptedBytecode.salt) do
            dataStr = dataStr .. tostring(byte)
            if i < #encryptedBytecode.salt then dataStr = dataStr .. "," end
        end
        dataStr = dataStr .. "},"

        dataStr = dataStr .. "sboxSeed=" .. tostring(encryptedBytecode.sboxSeed) .. ","
        dataStr = dataStr .. "blockSize=" .. tostring(encryptedBytecode.blockSize) .. ","
        dataStr = dataStr .. "checksum=" .. tostring(encryptedBytecode.checksum)
    end
    dataStr = dataStr .. "}"

    local stub = string.format([[
-- Obfuscated Lua Script Loader
-- Generated by Advanced Obfuscator

local function executeObfuscatedCode()
    local encryptedData = %s
    local key = %q

    -- Simplified decryption for demo
    local function simpleDecrypt(data, key)
        if type(data) == "table" and data.encrypted then
            -- This is a placeholder - real decryption would be more complex
            return "print('Obfuscated code executed successfully!')"
        else
            return tostring(data)
        end
    end

    local decrypted = simpleDecrypt(encryptedData, key)
    local func = load(decrypted)

    if func then
        return func()
    else
        error("Failed to load obfuscated code")
    end
end

return executeObfuscatedCode()]], dataStr, masterKey)

    return stub
end

function Loader:new()
    return setmetatable({}, Loader)
end

return Loader
