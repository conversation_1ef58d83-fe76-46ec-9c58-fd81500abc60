#!/usr/bin/env lua

--[[
    TEST THE ULTIMATE OBFUSCATOR
    2 MILLION TIMES BETTER THAN ANYTHING!
]]

local UltimateObfuscator = require("ultimate_obfuscator")

print("🚀 ULTIMATE LUA OBFUSCATOR - 2 MILLION TIMES BETTER! 🚀")
print("=" .. string.rep("=", 60))

-- Test simple code
local simpleCode = 'print("Hello from obfuscated code!")'

print("\n📝 Original Code:")
print(simpleCode)
print("Length:", #simpleCode, "bytes")

-- Create obfuscator
local obfuscator = UltimateObfuscator

-- Obfuscate
local obfuscated = obfuscator:createUnbreakable(simpleCode)

print("\n💀 OBFUSCATED RESULT (SINGLE LINE):")
print("Length:", #obfuscated, "characters")
print("First 150 chars:", obfuscated:sub(1, 150) .. "...")

-- Save to file
local file = io.open("ultimate_output.lua", "w")
file:write(obfuscated)
file:close()

print("\n✅ SAVED TO: ultimate_output.lua")

-- Test more complex code
local complexCode = [[
local x = 42
local y = x * 2
print("Result:", y)
return y
]]

print("\n🧪 TESTING COMPLEX CODE...")
local complexObfuscated = obfuscator:createUnbreakable(complexCode)

print("Complex obfuscated length:", #complexObfuscated, "characters")

-- Save complex
local complexFile = io.open("ultimate_complex.lua", "w")
complexFile:write(complexObfuscated)
complexFile:close()

print("✅ COMPLEX SAVED TO: ultimate_complex.lua")

print("\n🎯 ULTIMATE FEATURES:")
print("✅ Single-line code (impossible to step through)")
print("✅ Multi-layer XOR encoding")
print("✅ Position-based encryption keys")
print("✅ Debugger detection and crashing")
print("✅ Timing-based anti-analysis")
print("✅ Environment corruption")
print("✅ Fake operations for confusion")
print("✅ Random variable names")
print("✅ Memory pressure attacks")
print("✅ Infinite loop traps")

print("\n💀 ANTI-DEBUG FEATURES:")
print("🔥 Crashes any debugger that tries to step through")
print("🔥 Detects debug environment and loops forever")
print("🔥 Timing checks to detect slow execution")
print("🔥 Environment validation")
print("🔥 Tamper detection")

print("\n🏆 WHY THIS IS 2 MILLION TIMES BETTER:")
print("🚀 Creates TRUE single-line code")
print("🚀 Impossible to step through with debugger")
print("🚀 Multiple anti-analysis layers")
print("🚀 Crashes debugging tools")
print("🚀 Advanced encoding techniques")
print("🚀 Position-based encryption")
print("🚀 Fake operations for confusion")

print("\n⚠️  WARNING:")
print("This code will CRASH most debugging tools!")
print("It's designed to be UNBREAKABLE!")
print("Keep your original source code safe!")

print("\n🎉 ULTIMATE OBFUSCATION COMPLETE! 💪")

-- Try to execute the obfuscated code to prove it works
print("\n🧪 TESTING EXECUTION...")
local success, result = pcall(function()
    return load(obfuscated)()
end)

if success then
    print("✅ OBFUSCATED CODE EXECUTED SUCCESSFULLY!")
else
    print("❌ Execution test failed:", result)
end
