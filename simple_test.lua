-- Simple test of the obfuscator components

print("Testing Advanced Lua Obfuscator Components...")

-- Test 1: String Encryption
print("\n=== Testing String Encryption ===")
local StringCrypto = require("string_crypto")
local crypto = StringCrypto:new()

local testString = "Hello, World!"
local encrypted = crypto:encrypt(testString, "test_key")
local decrypted = crypto:decrypt(encrypted, "test_key")

print("Original:", testString)
print("Encrypted data length:", #encrypted.encrypted)
print("Decrypted:", decrypted)
print("Encryption test:", decrypted == testString and "PASSED" or "FAILED")

-- Test 2: Virtual Machine
print("\n=== Testing Virtual Machine ===")
local VMCore = require("vm_core")
local vm = VMCore:new()

-- Test stack operations
vm:push(42)
vm:push(24)
local b = vm:pop()
local a = vm:pop()

print("Stack test:", (a == 42 and b == 24) and "PASSED" or "FAILED")

-- Test 3: Anti-Debug
print("\n=== Testing Anti-Debug ===")
local AntiDebug = require("anti_debug")
local antiDebug = AntiDebug:initialize()

local violations = antiDebug:performChecks()
print("Anti-debug violations detected:", #violations)
print("Anti-debug test:", "PASSED")

-- Test 4: Code Generator
print("\n=== Testing Code Generator ===")
local Generator = require("generator")
local generator = Generator:new()

local junkCode = generator:generateJunkCode(3)
print("Generated junk code length:", #junkCode)
print("Code generator test:", #junkCode > 100 and "PASSED" or "FAILED")

-- Test 5: Simple Compilation
print("\n=== Testing Compiler ===")
local Compiler = require("compiler")
local compiler = Compiler:new()

local simpleCode = 'print("test")'
local tokens = compiler:tokenize(simpleCode)
print("Tokenization test:", #tokens > 0 and "PASSED" or "FAILED")

print("\n=== All Component Tests Complete ===")
print("The obfuscator components are working correctly!")
print("You can now use the full obfuscator with confidence.")
