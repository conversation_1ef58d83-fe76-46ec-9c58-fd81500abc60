--[[
    Bytecode Compiler
    Converts Lua AST to custom encrypted bytecode
]]

local Compiler = {}
Compiler.__index = Compiler

-- AST Node Types
local NODE_TYPES = {
    PROGRAM = "program",
    FUNCTION = "function",
    BLOCK = "block",
    STATEMENT = "statement",
    EXPRESSION = "expression",
    LITERAL = "literal",
    IDENTIFIER = "identifier",
    BINARY_OP = "binary_op",
    UNARY_OP = "unary_op",
    CALL = "call",
    TABLE = "table",
    INDEX = "index",
    ASSIGNMENT = "assignment",
    IF = "if",
    WHILE = "while",
    FOR = "for",
    RETURN = "return",
    BREAK = "break",
    LOCAL = "local"
}

-- Simple Lua lexer
function Compiler:tokenize(source)
    local tokens = {}
    local i = 1
    local line = 1
    local column = 1
    
    local keywords = {
        ["and"] = "AND", ["break"] = "BREAK", ["do"] = "DO", ["else"] = "ELSE",
        ["elseif"] = "ELSEIF", ["end"] = "END", ["false"] = "FALSE", ["for"] = "FOR",
        ["function"] = "FUNCTION", ["if"] = "IF", ["in"] = "IN", ["local"] = "LOCAL",
        ["nil"] = "NIL", ["not"] = "NOT", ["or"] = "OR", ["repeat"] = "REPEAT",
        ["return"] = "RETURN", ["then"] = "THEN", ["true"] = "TRUE", ["until"] = "UNTIL",
        ["while"] = "WHILE"
    }
    
    while i <= #source do
        local char = source:sub(i, i)
        
        -- Skip whitespace
        if char:match("%s") then
            if char == "\n" then
                line = line + 1
                column = 1
            else
                column = column + 1
            end
            i = i + 1
        -- Comments
        elseif char == "-" and source:sub(i + 1, i + 1) == "-" then
            -- Skip to end of line
            while i <= #source and source:sub(i, i) ~= "\n" do
                i = i + 1
            end
        -- String literals
        elseif char == '"' or char == "'" then
            local quote = char
            local str = ""
            i = i + 1
            while i <= #source and source:sub(i, i) ~= quote do
                local c = source:sub(i, i)
                if c == "\\" and i + 1 <= #source then
                    i = i + 1
                    c = source:sub(i, i)
                    if c == "n" then c = "\n"
                    elseif c == "t" then c = "\t"
                    elseif c == "r" then c = "\r"
                    elseif c == "\\" then c = "\\"
                    elseif c == quote then c = quote
                    end
                end
                str = str .. c
                i = i + 1
            end
            i = i + 1 -- Skip closing quote
            table.insert(tokens, {type = "STRING", value = str, line = line, column = column})
        -- Numbers
        elseif char:match("%d") then
            local num = ""
            while i <= #source and source:sub(i, i):match("[%d%.]") do
                num = num .. source:sub(i, i)
                i = i + 1
            end
            table.insert(tokens, {type = "NUMBER", value = tonumber(num), line = line, column = column})
        -- Identifiers and keywords
        elseif char:match("[%a_]") then
            local ident = ""
            while i <= #source and source:sub(i, i):match("[%w_]") do
                ident = ident .. source:sub(i, i)
                i = i + 1
            end
            local tokenType = keywords[ident] or "IDENTIFIER"
            table.insert(tokens, {type = tokenType, value = ident, line = line, column = column})
        -- Operators and punctuation
        else
            local operators = {
                ["=="] = "EQ", ["~="] = "NE", ["<="] = "LE", [">="] = "GE",
                [".."] = "CONCAT", ["..."] = "VARARG",
                ["+"] = "PLUS", ["-"] = "MINUS", ["*"] = "MUL", ["/"] = "DIV",
                ["%"] = "MOD", ["^"] = "POW", ["#"] = "LEN",
                ["<"] = "LT", [">"] = "GT", ["="] = "ASSIGN",
                ["("] = "LPAREN", [")"] = "RPAREN", ["{"] = "LBRACE", ["}"] = "RBRACE",
                ["["] = "LBRACKET", ["]"] = "RBRACKET", [";"] = "SEMICOLON",
                [","] = "COMMA", ["."] = "DOT", [":"] = "COLON"
            }
            
            -- Check for two-character operators first
            local twoChar = source:sub(i, i + 1)
            if operators[twoChar] then
                table.insert(tokens, {type = operators[twoChar], value = twoChar, line = line, column = column})
                i = i + 2
            elseif operators[char] then
                table.insert(tokens, {type = operators[char], value = char, line = line, column = column})
                i = i + 1
            else
                -- Unknown character, skip it
                i = i + 1
            end
        end
        column = column + 1
    end
    
    table.insert(tokens, {type = "EOF", value = "", line = line, column = column})
    return tokens
end

-- Simple recursive descent parser
function Compiler:parse(tokens)
    local parser = {
        tokens = tokens,
        current = 1
    }
    
    function parser:peek()
        return self.tokens[self.current]
    end
    
    function parser:advance()
        if self.current <= #self.tokens then
            self.current = self.current + 1
        end
        return self.tokens[self.current - 1]
    end
    
    function parser:match(...)
        local types = {...}
        for _, tokenType in ipairs(types) do
            if self:peek().type == tokenType then
                return self:advance()
            end
        end
        return nil
    end
    
    function parser:parseExpression()
        return self:parseOr()
    end
    
    function parser:parseOr()
        local left = self:parseAnd()
        while self:match("OR") do
            local operator = self.tokens[self.current - 1]
            local right = self:parseAnd()
            left = {
                type = NODE_TYPES.BINARY_OP,
                operator = operator.value,
                left = left,
                right = right
            }
        end
        return left
    end
    
    function parser:parseAnd()
        local left = self:parseEquality()
        while self:match("AND") do
            local operator = self.tokens[self.current - 1]
            local right = self:parseEquality()
            left = {
                type = NODE_TYPES.BINARY_OP,
                operator = operator.value,
                left = left,
                right = right
            }
        end
        return left
    end
    
    function parser:parseEquality()
        local left = self:parseComparison()
        while self:match("EQ", "NE") do
            local operator = self.tokens[self.current - 1]
            local right = self:parseComparison()
            left = {
                type = NODE_TYPES.BINARY_OP,
                operator = operator.value,
                left = left,
                right = right
            }
        end
        return left
    end
    
    function parser:parseComparison()
        local left = self:parseConcatenation()
        while self:match("LT", "LE", "GT", "GE") do
            local operator = self.tokens[self.current - 1]
            local right = self:parseConcatenation()
            left = {
                type = NODE_TYPES.BINARY_OP,
                operator = operator.value,
                left = left,
                right = right
            }
        end
        return left
    end
    
    function parser:parseConcatenation()
        local left = self:parseAddition()
        while self:match("CONCAT") do
            local operator = self.tokens[self.current - 1]
            local right = self:parseAddition()
            left = {
                type = NODE_TYPES.BINARY_OP,
                operator = operator.value,
                left = left,
                right = right
            }
        end
        return left
    end
    
    function parser:parseAddition()
        local left = self:parseMultiplication()
        while self:match("PLUS", "MINUS") do
            local operator = self.tokens[self.current - 1]
            local right = self:parseMultiplication()
            left = {
                type = NODE_TYPES.BINARY_OP,
                operator = operator.value,
                left = left,
                right = right
            }
        end
        return left
    end
    
    function parser:parseMultiplication()
        local left = self:parseUnary()
        while self:match("MUL", "DIV", "MOD") do
            local operator = self.tokens[self.current - 1]
            local right = self:parseUnary()
            left = {
                type = NODE_TYPES.BINARY_OP,
                operator = operator.value,
                left = left,
                right = right
            }
        end
        return left
    end
    
    function parser:parseUnary()
        if self:match("NOT", "MINUS", "LEN") then
            local operator = self.tokens[self.current - 1]
            local operand = self:parseUnary()
            return {
                type = NODE_TYPES.UNARY_OP,
                operator = operator.value,
                operand = operand
            }
        end
        return self:parsePower()
    end
    
    function parser:parsePower()
        local left = self:parsePrimary()
        if self:match("POW") then
            local operator = self.tokens[self.current - 1]
            local right = self:parseUnary() -- Right associative
            left = {
                type = NODE_TYPES.BINARY_OP,
                operator = operator.value,
                left = left,
                right = right
            }
        end
        return left
    end
    
    function parser:parsePrimary()
        if self:match("TRUE") then
            return {type = NODE_TYPES.LITERAL, value = true}
        elseif self:match("FALSE") then
            return {type = NODE_TYPES.LITERAL, value = false}
        elseif self:match("NIL") then
            return {type = NODE_TYPES.LITERAL, value = nil}
        elseif self:match("NUMBER") then
            return {type = NODE_TYPES.LITERAL, value = self.tokens[self.current - 1].value}
        elseif self:match("STRING") then
            return {type = NODE_TYPES.LITERAL, value = self.tokens[self.current - 1].value}
        elseif self:match("IDENTIFIER") then
            local name = self.tokens[self.current - 1].value
            -- Check for function call
            if self:match("LPAREN") then
                local args = {}
                while not self:match("RPAREN") do
                    table.insert(args, self:parseExpression())
                    if not self:match("COMMA") then
                        self:match("RPAREN")
                        break
                    end
                end
                return {
                    type = NODE_TYPES.CALL,
                    name = name,
                    arguments = args
                }
            else
                return {type = NODE_TYPES.IDENTIFIER, name = name}
            end
        elseif self:match("LPAREN") then
            local expr = self:parseExpression()
            self:match("RPAREN")
            return expr
        elseif self:match("LBRACE") then
            -- Table literal
            local elements = {}
            while not self:match("RBRACE") do
                table.insert(elements, self:parseExpression())
                if not self:match("COMMA") then
                    self:match("RBRACE")
                    break
                end
            end
            return {
                type = NODE_TYPES.TABLE,
                elements = elements
            }
        end

        -- If we can't parse anything, return a placeholder
        return {type = NODE_TYPES.LITERAL, value = nil}
    end
    
    function parser:parseStatement()
        if self:match("RETURN") then
            local expr = nil
            if self:peek().type ~= "EOF" and self:peek().type ~= "END" then
                expr = self:parseExpression()
            end
            return {
                type = NODE_TYPES.RETURN,
                expression = expr
            }
        elseif self:match("LOCAL") then
            if self:match("FUNCTION") then
                -- Local function declaration
                local name = self:match("IDENTIFIER")
                self:match("LPAREN")
                local params = {}
                while not self:match("RPAREN") do
                    local param = self:match("IDENTIFIER")
                    if param then
                        table.insert(params, param.value)
                    end
                    if not self:match("COMMA") then
                        self:match("RPAREN")
                        break
                    end
                end
                -- Skip function body for now
                return {
                    type = NODE_TYPES.FUNCTION,
                    name = name and name.value or "anonymous",
                    params = params
                }
            else
                -- Local variable declaration
                local names = {}
                repeat
                    local name = self:match("IDENTIFIER")
                    if name then
                        table.insert(names, name.value)
                    end
                until not self:match("COMMA")

                local values = {}
                if self:match("ASSIGN") then
                    repeat
                        table.insert(values, self:parseExpression())
                    until not self:match("COMMA")
                end

                return {
                    type = NODE_TYPES.LOCAL,
                    names = names,
                    values = values
                }
            end
        else
            local expr = self:parseExpression()
            return {
                type = NODE_TYPES.STATEMENT,
                expression = expr
            }
        end
    end
    
    function parser:parseProgram()
        local statements = {}
        while self:peek().type ~= "EOF" do
            table.insert(statements, self:parseStatement())
        end
        return {
            type = NODE_TYPES.PROGRAM,
            statements = statements
        }
    end
    
    return parser:parseProgram()
end

function Compiler:new()
    local compiler = setmetatable({}, Compiler)
    compiler.constants = {}
    compiler.constantMap = {}
    compiler.instructions = {}
    return compiler
end

function Compiler:addConstant(value)
    if self.constantMap[value] then
        return self.constantMap[value]
    end
    
    table.insert(self.constants, value)
    local index = #self.constants
    self.constantMap[value] = index
    return index
end

function Compiler:emit(opcode, arg1, arg2, arg3)
    arg1 = arg1 or 0
    arg2 = arg2 or 0
    arg3 = arg3 or 0
    
    local instruction = opcode | (arg1 << 8) | (arg2 << 16) | (arg3 << 24)
    table.insert(self.instructions, instruction)
end

return Compiler
