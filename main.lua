#!/usr/bin/env lua

--[[
    Advanced Lua Obfuscator - Main Entry Point
    MoonSec/Luraph Level Obfuscation System
    
    Features:
    - Custom Virtual Machine with encrypted bytecode
    - Multi-layer string encryption with decoys
    - Control flow flattening and state machines
    - Anti-debugging and tamper detection
    - Junk code generation and fake functions
    - Variable name obfuscation
    - Roblox compatibility
    
    Usage:
    lua main.lua input.lua output.lua
]]

local ObfuscatorCore = require("obfuscator")

-- Command line argument parsing
local function parseArguments(args)
    if #args < 1 then
        print("Usage: lua main.lua <input_file> [output_file]")
        print("       lua main.lua --demo")
        print("       lua main.lua --test")
        os.exit(1)
    end

    return args[1], args[2] or "obfuscated_output.lua"
end

-- Read file contents
local function readFile(filename)
    local file = io.open(filename, "r")
    if not file then
        error("Cannot open file: " .. filename)
    end
    
    local content = file:read("*all")
    file:close()
    return content
end

-- Write file contents
local function writeFile(filename, content)
    local file = io.open(filename, "w")
    if not file then
        error("Cannot create file: " .. filename)
    end
    
    file:write(content)
    file:close()
end

-- Demo mode - obfuscate a sample script
local function runDemo()
    print("=== Advanced Lua Obfuscator Demo ===")
    print("Generating sample script to obfuscate...")
    
    local sampleScript = [[
-- Sample Lua script for obfuscation
local function fibonacci(n)
    if n <= 1 then
        return n
    end
    return fibonacci(n - 1) + fibonacci(n - 2)
end

local function greet(name)
    print("Hello, " .. (name or "World") .. "!")
    return "Greeting sent to " .. (name or "World")
end

-- Main execution
local result = fibonacci(10)
print("Fibonacci(10) = " .. result)

greet("Obfuscator")

-- Table manipulation
local data = {
    numbers = {1, 2, 3, 4, 5},
    strings = {"hello", "world", "lua"},
    nested = {
        level1 = {
            level2 = "deep value"
        }
    }
}

for i, num in ipairs(data.numbers) do
    print("Number " .. i .. ": " .. num)
end

-- Conditional logic
if result > 50 then
    print("Result is large")
else
    print("Result is small")
end

-- Return final result
return {
    fibonacci_result = result,
    data_processed = true,
    timestamp = os.time()
}]]

    print("Sample script created. Starting obfuscation...")
    
    local obfuscator = ObfuscatorCore:new()
    local obfuscatedCode = obfuscator:obfuscate(sampleScript)
    
    writeFile("demo_obfuscated.lua", obfuscatedCode)
    
    print("Demo complete! Obfuscated code saved to: demo_obfuscated.lua")
    print("Original size: " .. #sampleScript .. " bytes")
    print("Obfuscated size: " .. #obfuscatedCode .. " bytes")
    print("Size increase: " .. math.floor((#obfuscatedCode / #sampleScript - 1) * 100) .. "%")
end

-- Test mode - run comprehensive tests
local function runTests()
    print("=== Running Obfuscator Tests ===")
    
    -- Test 1: Basic obfuscation
    print("Test 1: Basic obfuscation...")
    local simpleCode = 'print("Hello, World!")'
    local obfuscator = ObfuscatorCore:new()
    local obfuscated = obfuscator:obfuscate(simpleCode)
    print("✓ Basic obfuscation successful")
    
    -- Test 2: String encryption
    print("Test 2: String encryption...")
    local StringCrypto = require("string_crypto")
    local crypto = StringCrypto:new()
    local encrypted = crypto:encrypt("test string", "test key")
    local decrypted = crypto:decrypt(encrypted, "test key")
    assert(decrypted == "test string", "String encryption/decryption failed")
    print("✓ String encryption successful")
    
    -- Test 3: VM execution
    print("Test 3: Virtual machine...")
    local VMCore = require("vm_core")
    local vm = VMCore:new()
    vm:push(10)
    vm:push(20)
    assert(vm:pop() == 20, "VM stack operation failed")
    assert(vm:pop() == 10, "VM stack operation failed")
    print("✓ Virtual machine successful")
    
    -- Test 4: Anti-debug
    print("Test 4: Anti-debug protection...")
    local AntiDebug = require("anti_debug")
    local antiDebug = AntiDebug:initialize()
    local violations = antiDebug:performChecks()
    print("✓ Anti-debug protection successful (violations: " .. #violations .. ")")
    
    -- Test 5: Code generation
    print("Test 5: Code generation...")
    local Generator = require("generator")
    local generator = Generator:new()
    local junkCode = generator:generateJunkCode(5)
    assert(#junkCode > 100, "Junk code generation failed")
    print("✓ Code generation successful")
    
    print("All tests passed! ✓")
end

-- Roblox-specific optimizations
local function optimizeForRoblox(code)
    -- Add Roblox-specific compatibility
    local robloxWrapper = string.format([[
-- Roblox Compatibility Layer
if game and workspace then
    -- Running in Roblox environment
    local Players = game:GetService("Players")
    local RunService = game:GetService("RunService")
    local ReplicatedStorage = game:GetService("ReplicatedStorage")
    
    -- Obfuscated code execution
    spawn(function()
        %s
    end)
else
    -- Running in standard Lua environment
    %s
end]], code, code)
    
    return robloxWrapper
end

-- Main execution
local function main(args)
    local inputFile, outputFile = parseArguments(args)
    
    if inputFile == "--demo" then
        runDemo()
        return
    elseif inputFile == "--test" then
        runTests()
        return
    end
    
    print("Advanced Lua Obfuscator v1.0")
    print("Input file: " .. inputFile)
    print("Output file: " .. outputFile)
    print()
    
    -- Read input file
    print("Reading input file...")
    local sourceCode = readFile(inputFile)
    print("Source code size: " .. #sourceCode .. " bytes")
    
    -- Initialize obfuscator
    print("Initializing obfuscator...")
    local obfuscator = ObfuscatorCore:new()
    
    -- Perform obfuscation
    print("Starting obfuscation process...")
    local startTime = os.clock()
    local obfuscatedCode = obfuscator:obfuscate(sourceCode)
    local endTime = os.clock()
    
    -- Optimize for Roblox if needed
    if string.find(sourceCode, "game") or string.find(sourceCode, "workspace") then
        print("Roblox environment detected, applying optimizations...")
        obfuscatedCode = optimizeForRoblox(obfuscatedCode)
    end
    
    -- Write output file
    print("Writing obfuscated code...")
    writeFile(outputFile, obfuscatedCode)
    
    -- Statistics
    print()
    print("=== Obfuscation Complete ===")
    print("Processing time: " .. string.format("%.2f", endTime - startTime) .. " seconds")
    print("Original size: " .. #sourceCode .. " bytes")
    print("Obfuscated size: " .. #obfuscatedCode .. " bytes")
    print("Size increase: " .. math.floor((#obfuscatedCode / #sourceCode - 1) * 100) .. "%")
    print("Output saved to: " .. outputFile)
    print()
    print("Features applied:")
    print("✓ Custom VM bytecode encryption")
    print("✓ Multi-layer string encryption")
    print("✓ Control flow flattening")
    print("✓ Anti-debugging protection")
    print("✓ Junk code generation")
    print("✓ Variable name obfuscation")
    print("✓ Fake function insertion")
    print("✓ Tamper detection traps")
    
    if string.find(obfuscatedCode, "game") then
        print("✓ Roblox compatibility layer")
    end
    
    print()
    print("WARNING: Keep the obfuscated file secure!")
    print("This level of obfuscation makes reverse engineering extremely difficult.")
end

-- Error handling wrapper
local function safeMain()
    local args = arg or {}
    local success, error = pcall(main, args)
    if not success then
        print("ERROR: " .. tostring(error))
        print()
        print("If you encounter issues:")
        print("1. Ensure all required modules are present")
        print("2. Check input file syntax")
        print("3. Verify file permissions")
        print("4. Try running with --test to verify installation")
        os.exit(1)
    end
end

-- Execute main function
safeMain()
