#!/usr/bin/env lua

-- Test the REAL obfuscator
local RealObfuscator = require("real_obfuscator")

print("🔥 TESTING REAL OBFUSCATOR THAT ACTUALLY WORKS")
print("=" .. string.rep("=", 50))

-- Test with simple code
local testCode = [[
print("Hello from obfuscated code!")
local x = 42
local y = x * 2
print("Result:", y)
return y
]]

print("📝 Original Code:")
print(testCode)

-- Create obfuscator and test
local obfuscator = RealObfuscator
local obfuscated = obfuscator:test()

print("\n💾 Saving obfuscated code to file...")
local file = io.open("working_obfuscated.lua", "w")
file:write(obfuscated)
file:close()

print("✅ Saved to: working_obfuscated.lua")

-- Test with more complex code
print("\n🧪 Testing with complex code...")
local complexCode = [[
local function fibonacci(n)
    if n <= 1 then
        return n
    end
    return fibonacci(n-1) + fibon<PERSON>ci(n-2)
end

local result = fibonacci(8)
print("Fibonacci(8) =", result)

local data = {
    name = "secret",
    value = 12345
}

for k, v in pairs(data) do
    print(k .. ":", v)
end

return result
]]

print("Complex code length:", #complexCode, "bytes")
local complexObfuscated = obfuscator:obfuscate(complexCode)

print("Obfuscated length:", #complexObfuscated, "bytes")
print("Size increase:", math.floor(#complexObfuscated / #complexCode * 100) .. "%")

-- Test execution
print("\n🧪 Testing complex obfuscated execution...")
local success, result = pcall(function()
    return load(complexObfuscated)()
end)

if success then
    print("✅ COMPLEX OBFUSCATED CODE WORKS! Result:", result)
else
    print("❌ Error:", result)
end

-- Save complex version
local complexFile = io.open("complex_working.lua", "w")
complexFile:write(complexObfuscated)
complexFile:close()

print("✅ Complex version saved to: complex_working.lua")

print("\n🎯 FEATURES OF THIS OBFUSCATOR:")
print("✅ Actually works and executes properly")
print("✅ String encryption with XOR and position-based keys")
print("✅ Variable name obfuscation (like MoonSec)")
print("✅ Fake function generation")
print("✅ Anti-debugging protection")
print("✅ Timing-based tamper detection")
print("✅ Environment validation")

print("\n🔥 This obfuscator creates WORKING code that's hard to deobfuscate!")
print("Ready for Discord bot integration! 🤖")
