--[[
    Advanced Code Generator
    Creates junk code, fake functions, and variable obfuscation
]]

local Generator = {}
Generator.__index = Generator

-- Character sets for name generation
local CHAR_SETS = {
    letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",
    numbers = "0123456789",
    special = "_",
    unicode = "αβγδεζηθικλμνξοπρστυφχψωΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ"
}

-- Advanced random name generator
local function generateObfuscatedName(length, useUnicode)
    length = length or math.random(8, 16)
    local chars = CHAR_SETS.letters .. CHAR_SETS.special
    if useUnicode then
        chars = chars .. CHAR_SETS.unicode
    end

    local name = ""
    -- First character must be letter or underscore
    local firstChars = CHAR_SETS.letters .. CHAR_SETS.special
    local firstIndex = math.random(1, #firstChars)
    name = name .. string.sub(firstChars, firstIndex, firstIndex)

    -- Rest can include numbers
    chars = chars .. CHAR_SETS.numbers
    for i = 2, length do
        local charIndex = math.random(1, #chars)
        name = name .. string.sub(chars, charIndex, charIndex)
    end

    return name
end

-- Generate realistic fake mathematical functions
local function generateMathFunction()
    local funcName = generateObfuscatedName()
    local operations = {
        "return math.sin(x) * math.cos(y)",
        "return math.sqrt(x * x + y * y)",
        "return (x + y) / (x - y + 1)",
        "return math.floor(x * math.pi) % 256",
        "return math.abs(x) + math.abs(y)",
        "return x ^ 2 + y ^ 2 - x * y",
        "return math.log(math.abs(x) + 1) * math.exp(y / 10)",
        "return (x * 69069 + 1) % 2147483647",
        "return math.atan2(y, x) * 180 / math.pi"
    }
    
    local operation = operations[math.random(1, #operations)]
    return string.format([[
local function %s(x, y)
    x = x or math.random()
    y = y or math.random()
    %s
end]], funcName, operation)
end

-- Generate fake string manipulation functions
local function generateStringFunction()
    local funcName = generateObfuscatedName()
    local operations = {
        "return string.reverse(str)",
        "return string.upper(str):gsub('[AEIOU]', '*')",
        "return string.rep(str, math.min(3, #str))",
        "return str:gsub('.', function(c) return string.char((string.byte(c) + 1) % 256) end)",
        "return string.format('%q', str):sub(2, -2)",
        "return str:gsub('%w', function(c) return string.char(string.byte(c) ~ 42) end)"
    }
    
    local operation = operations[math.random(1, #operations)]
    return string.format([[
local function %s(str)
    str = str or "default"
    %s
end]], funcName, operation)
end

-- Generate fake table manipulation functions
local function generateTableFunction()
    local funcName = generateObfuscatedName()
    local operations = {
        "return #tbl + math.random(0, 10)",
        "local copy = {} for k,v in pairs(tbl) do copy[k] = v end return copy",
        "local sum = 0 for _,v in pairs(tbl) do if type(v) == 'number' then sum = sum + v end end return sum",
        "local keys = {} for k,_ in pairs(tbl) do table.insert(keys, k) end return keys",
        "return tbl[math.random(1, #tbl)] or tbl[next(tbl)]"
    }
    
    local operation = operations[math.random(1, #operations)]
    return string.format([[
local function %s(tbl)
    tbl = tbl or {}
    %s
end]], funcName, operation)
end

-- Generate fake cryptographic-looking functions
local function generateCryptoFunction()
    local funcName = generateObfuscatedName()
    local operations = {
        [[
    local result = 0
    for i = 1, #data do
        result = result ~ (string.byte(data, i) * key)
        key = (key * 1103515245 + 12345) % 2147483648
    end
    return result]],
        [[
    local hash = 5381
    for i = 1, #data do
        hash = ((hash << 5) + hash) + string.byte(data, i)
    end
    return hash % 4294967296]],
        [[
    local result = ""
    for i = 1, #data do
        local char = string.byte(data, i)
        char = char ~ (key % 256)
        result = result .. string.char(char)
        key = (key + 1) % 256
    end
    return result]]
    }
    
    local operation = operations[math.random(1, #operations)]
    return string.format([[
local function %s(data, key)
    data = data or "sample"
    key = key or 42
    %s
end]], funcName, operation)
end

-- Generate fake class-like structures
local function generateFakeClass()
    local className = generateObfuscatedName()
    local methods = {}
    
    for i = 1, math.random(3, 7) do
        local methodName = generateObfuscatedName(6)
        local methodBody = {
            "return self.value * 2",
            "self.value = self.value + 1 return self",
            "return type(self.value)",
            "return string.format('Object<%s>', tostring(self.value))",
            "return self.value > 0"
        }
        
        table.insert(methods, string.format([[
    function obj:%s()
        %s
    end]], methodName, methodBody[math.random(1, #methodBody)]))
    end
    
    return string.format([[
local %s = {}
%s.__index = %s

function %s:new(value)
    local obj = setmetatable({}, %s)
    obj.value = value or math.random(1, 100)
    return obj
end

%s]], className, className, className, className, className, table.concat(methods, "\n"))
end

-- Generate complex nested structures
local function generateNestedStructure()
    local varName = generateObfuscatedName()
    local depth = math.random(2, 5)
    
    local function buildNested(currentDepth)
        if currentDepth <= 0 then
            return tostring(math.random(1, 1000))
        end
        
        local items = {}
        for i = 1, math.random(2, 4) do
            local key = generateObfuscatedName(4)
            local value = buildNested(currentDepth - 1)
            table.insert(items, string.format('["%s"] = %s', key, value))
        end
        
        return "{" .. table.concat(items, ", ") .. "}"
    end
    
    return string.format("local %s = %s", varName, buildNested(depth))
end

-- Generate fake loop constructs
local function generateFakeLoop()
    local loopTypes = {
        function()
            local var = generateObfuscatedName(4)
            local limit = math.random(10, 100)
            return string.format([[
for %s = 1, %d do
    local temp = %s * math.pi
    if temp > %d then break end
end]], var, limit, var, limit / 2)
        end,
        function()
            local var = generateObfuscatedName(4)
            local table_name = generateObfuscatedName(6)
            return string.format([[
local %s = {1, 2, 3, 4, 5}
for _, %s in ipairs(%s) do
    local result = %s ^ 2
end]], table_name, var, table_name, var)
        end,
        function()
            local var = generateObfuscatedName(4)
            return string.format([[
local %s = 0
while %s < 50 do
    %s = %s + math.random(1, 5)
    if %s %% 7 == 0 then break end
end]], var, var, var, var, var)
        end
    }
    
    local loopType = loopTypes[math.random(1, #loopTypes)]
    return loopType()
end

-- Generate fake conditional blocks
local function generateFakeConditional()
    local var1 = generateObfuscatedName(4)
    local var2 = generateObfuscatedName(4)
    local conditions = {
        string.format("%s > %s", var1, var2),
        string.format("%s == nil", var1),
        string.format("type(%s) == 'number'", var1),
        string.format("%s %% 2 == 0", var1),
        string.format("#tostring(%s) > 3", var1)
    }
    
    local condition = conditions[math.random(1, #conditions)]
    local actions = {
        string.format("%s = %s + 1", var1, var1),
        string.format("%s = tostring(%s)", var2, var1),
        string.format("%s = math.abs(%s)", var1, var1),
        "-- placeholder action"
    }
    
    local action = actions[math.random(1, #actions)]
    
    return string.format([[
local %s = math.random(1, 100)
local %s = math.random(1, 100)
if %s then
    %s
else
    %s = %s * 2
end]], var1, var2, condition, action, var2, var2)
end

-- Main junk code generator
function Generator:generateJunkCode(complexity)
    complexity = complexity or 10
    local junkPieces = {}
    
    for i = 1, complexity do
        local generators = {
            generateMathFunction,
            generateStringFunction,
            generateTableFunction,
            generateCryptoFunction,
            generateFakeClass,
            generateNestedStructure,
            generateFakeLoop,
            generateFakeConditional
        }
        
        local generator = generators[math.random(1, #generators)]
        table.insert(junkPieces, generator())
        
        -- Add some random comments
        if math.random() < 0.3 then
            local comments = {
                "-- Optimization checkpoint",
                "-- Memory management",
                "-- Performance critical section",
                "-- Security validation",
                "-- Compatibility layer"
            }
            table.insert(junkPieces, comments[math.random(1, #comments)])
        end
    end
    
    return table.concat(junkPieces, "\n\n")
end

-- Generate variable name mapping
function Generator:generateVariableMap(originalNames)
    local mapping = {}
    local used = {}
    
    for _, name in ipairs(originalNames) do
        local newName
        repeat
            newName = generateObfuscatedName(math.random(8, 20), math.random() < 0.1)
        until not used[newName]
        
        used[newName] = true
        mapping[name] = newName
    end
    
    return mapping
end

-- Generate fake function calls that do nothing
function Generator:generateFakeCalls(count)
    count = count or 20
    local calls = {}
    
    for i = 1, count do
        local funcName = generateObfuscatedName()
        local argCount = math.random(0, 3)
        local args = {}
        
        for j = 1, argCount do
            local argTypes = {"nil", "true", "false", tostring(math.random(1, 100)), '"fake"'}
            table.insert(args, argTypes[math.random(1, #argTypes)])
        end
        
        local call = string.format("local %s = function(%s) end", 
            funcName, 
            table.concat(args, ", "))
        table.insert(calls, call)
    end
    
    return table.concat(calls, "\n")
end

-- Create control flow flattening structure
function Generator:generateControlFlowFlattening(states)
    states = states or 10
    local stateVar = generateObfuscatedName()
    local cases = {}
    
    for i = 1, states do
        local nextState = (i % states) + 1
        local case = string.format([[
        [%d] = function()
            -- State %d logic
            local temp = math.random(1, 100)
            if temp > 50 then
                %s = %d
            else
                %s = %d
            end
        end]], i, i, stateVar, nextState, stateVar, (nextState % states) + 1)
        table.insert(cases, case)
    end
    
    return string.format([[
local %s = 1
local states = {
%s
}

while %s > 0 and %s <= %d do
    if states[%s] then
        states[%s]()
    else
        break
    end
end]], stateVar, table.concat(cases, ",\n"), stateVar, stateVar, states, stateVar, stateVar)
end

function Generator:new()
    return setmetatable({}, Generator)
end

return Generator
